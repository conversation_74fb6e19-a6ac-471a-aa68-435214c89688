package com.tripudiotech.migration.service.processor.file.handler;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import io.smallrye.mutiny.Uni;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class ColumnDefinitionHandlerTest {

    @InjectMocks
    private ColumnDefinitionHandler columnDefinitionHandler;

    @Test
    public void testHandleFromExistingEntityData() {
        // Create test data
        ColumnDefinition columnDefinition = new ColumnDefinition();
        columnDefinition.setFieldType("FROM_EXISTING_ENTITY");
        columnDefinition.setTargetField("EngineeringName.name");

        String columnValue = "TestPart123";
        RowExtractedData rowExtractedData = new RowExtractedData();

        // Call the method
        Uni<Void> result = columnDefinitionHandler.handleFromExistingEntityData(
                "testTenant", "testToken", columnDefinition, columnValue, rowExtractedData);

        // Wait for the result
        result.await().indefinitely();

        // Verify the result
        assertTrue(rowExtractedData.isUseExistingEntity());
        assertEquals("EngineeringName", rowExtractedData.getExistingEntityType());
        assertEquals("name", rowExtractedData.getExistingEntityField());
        assertEquals("TestPart123", rowExtractedData.getExistingEntityValue());
    }

    @Test
    public void testHandleFromExistingEntityData_InvalidFormat() {
        // Create test data with invalid target format
        ColumnDefinition columnDefinition = new ColumnDefinition();
        columnDefinition.setFieldType("FROM_EXISTING_ENTITY");
        columnDefinition.setTargetField("InvalidFormat");

        String columnValue = "TestPart123";
        RowExtractedData rowExtractedData = new RowExtractedData();

        // Call the method and expect an exception
        Uni<Void> result = columnDefinitionHandler.handleFromExistingEntityData(
                "testTenant", "testToken", columnDefinition, columnValue, rowExtractedData );

        // Verify that an exception is thrown
        assertThrows(IllegalArgumentException.class, () -> result.await().indefinitely());
    }
}
