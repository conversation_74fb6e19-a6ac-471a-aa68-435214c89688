package com.tripudiotech.migration.util;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.Attribute;

import com.tripudiotech.datalib.model.EntityType;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.processor.file.validator.CommonHeaderValidator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
class CommonHeaderValidatorTest {

    @Mock
    private EntitySchema entitySchema;

    @Mock
    private EntityType entityType;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        when(entityType.getName()).thenReturn("Product");
        when(entitySchema.getEntityType()).thenReturn(entityType);

        Map<String, Attribute> attributes = new HashMap<>();
        when(entitySchema.getAttributes()).thenReturn(attributes);
    }

    List<DataMapping> defaultMapping() {
        List<DataMapping> mappings;
        mappings = new ArrayList<>();
        mappings.add(DataMapping.forEntity("Name", "name", ObjectType.ATTRIBUTE));
        mappings.add(DataMapping.forEntity("Description", "description", ObjectType.ATTRIBUTE));
        mappings.add(DataMapping.forEntity("Type", "type", ObjectType.ATTRIBUTE));
        mappings.add(DataMapping.forEntity("Status", "status", ObjectType.ATTRIBUTE));
        mappings.add(DataMapping.forEntity("Parent", "parent", ObjectType.RELATION));
        mappings.add(DataMapping.forEntity("Life state", "lifestate", ObjectType.LIFE_CYCLE));
        mappings.add(DataMapping.forRegularBom("Component ID", "componentId", ObjectType.COMPONENT));
        mappings.add(DataMapping.forRegularBom("Assembly ID", "assemblyId", ObjectType.ASSEMBLY));
        mappings.add(DataMapping.forEntity("Alt Name", "name", ObjectType.ATTRIBUTE));
        return mappings;
    }


    @Nested
    @DisplayName("validateRequiredHeaders tests")
    class ValidateRequiredHeadersTests {

        @Test
        @DisplayName("Should not throw exception when all required headers are present")
        void allRequiredHeadersPresent() {
            // Arrange
            String[] headers = {"Name", "Description", "Type"};

            Map<String, Attribute> attributes = createAttributesMap();
            when(entitySchema.getAttributes()).thenReturn(attributes);

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act & Assert
            assertDoesNotThrow(() -> validator.validateRequiredHeaders(headers, entitySchema));

            // Verify interactions
            verify(entitySchema, times(2)).getAttributes();
            verify(entitySchema, times(1)).getEntityType();
        }

        @Test
        @DisplayName("Should throw exception when required header is missing")
        void missingRequiredHeader() {
            // Arrange
            String[] headers = {"Description", "Type"}; // Missing "Name" which is required

            Map<String, Attribute> attributes = createAttributesMap();
            when(entitySchema.getAttributes()).thenReturn(attributes);

            List<DataMapping> mappings = new ArrayList<>();
            mappings.add(DataMapping.forEntity("Description", "desc", ObjectType.ATTRIBUTE));
            mappings.add(DataMapping.forEntity("Type", "type", ObjectType.ATTRIBUTE));

            CommonHeaderValidator validator = new CommonHeaderValidator(mappings);

            // Act & Assert
            FileValidatorException exception = assertThrows(FileValidatorException.class,
                    () -> validator.validateRequiredHeaders(headers, entitySchema));

            assertTrue(exception.getMessage().contains("Missing required fields"));
            assertTrue(exception.getMessage().contains("name"));
            assertTrue(exception.getMessage().contains("Product"));
        }

        @Test
        @DisplayName("Should handle empty headers array")
        void emptyHeadersArray() {
            // Arrange
            String[] headers = {};

            Map<String, Attribute> attributes = createAttributesMap();
            when(entitySchema.getAttributes()).thenReturn(attributes);

            CommonHeaderValidator validator = new CommonHeaderValidator(Collections.emptyList());

            // Act & Assert
            FileValidatorException exception = assertThrows(FileValidatorException.class,
                    () -> validator.validateRequiredHeaders(headers, entitySchema));

            assertTrue(exception.getMessage().contains("No headers found in the file"));
        }

        @Test
        @DisplayName("Should handle headers that don't match any mappings")
        void headersWithNoMappings() {
            // Arrange
            String[] headers = {"Unknown1", "Unknown2", "Unknown3"};

            Map<String, Attribute> attributes = createAttributesMap();
            when(entitySchema.getAttributes()).thenReturn(attributes);

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(Collections.emptyList());

            // Act & Assert
            FileValidatorException exception = assertThrows(FileValidatorException.class,
                    () -> validator.validateRequiredHeaders(headers, entitySchema));

            assertTrue(exception.getMessage().contains("Missing required fields"));
        }

        @Test
        @DisplayName("Should match header using alternate column name for the same target")
        void headerMatchingAlternateColumnName() {
            // Arrange
            String[] headers = {"Alt Name", "Description", "Type"}; // Using "Alt Name" instead of "Name"

            Map<String, Attribute> attributes = createAttributesMap();
            when(entitySchema.getAttributes()).thenReturn(attributes);

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act & Assert
            assertDoesNotThrow(() -> validator.validateRequiredHeaders(headers, entitySchema));
        }

        @Test
        @DisplayName("Should consider attributes with default values as not required")
        void attributesWithDefaultValuesNotRequired() {
            // Arrange
            String[] headers = {"Name", "Type"}; // Missing "Status" which has a default value

            Map<String, Attribute> attributes = createAttributesMap();
            when(entitySchema.getAttributes()).thenReturn(attributes);

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act & Assert
            assertDoesNotThrow(() -> validator.validateRequiredHeaders(headers, entitySchema));
        }
    }

    @Nested
    @DisplayName("columnDefinitionByColumnIndex tests")
    class ColumnDefinitionByColumnIndexTests {

        @Test
        @DisplayName("Should return correct mapping for valid headers")
        void validHeaders() {
            // Arrange
            List<String> headers = Arrays.asList("Name", "Description", "Component ID", "Assembly ID", "Life state", "Parent");
            Map<String, Attribute> attributeMap = createAttributesMap();

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act
            Map<Integer, List<ColumnDefinition>> result =
                    validator.columnDefinitionByColumnIndex(headers, attributeMap);

            // Assert
            assertEquals(6, result.size());

            // Check Name mapping
            assertColumnDefinition(result.get(0), "ATTRIBUTE", "name", "name", "STRING", null);

            // Check Description mapping
            assertColumnDefinition(result.get(1), "ATTRIBUTE", "description", "description", "STRING", null);

            // Check Component ID mapping
            assertColumnDefinition(result.get(2), "COMPONENT", "componentId", "componentId", "COMPONENT", null);

            // Check Assembly ID mapping
            assertColumnDefinition(result.get(3), "ASSEMBLY", "assemblyId", "assemblyId", "ASSEMBLY", null);

            // Check Life state mapping
            assertColumnDefinition(result.get(4), "LIFE_CYCLE", "lifestate", "lifestate", "LIFE_CYCLE", null);

            // Check Relation mapping
            assertColumnDefinition(result.get(5), "RELATION", "parent", "parent", "RELATION", null);
        }

        @Test
        @DisplayName("Should handle empty headers list")
        void emptyHeadersList() {
            // Arrange
            List<String> headers = Collections.emptyList();
            Map<String, Attribute> attributeMap = createAttributesMap();

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act
            Map<Integer, List<ColumnDefinition>> result =
                    validator.columnDefinitionByColumnIndex(headers, attributeMap);

            // Assert
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Should handle headers with no mappings")
        void headersWithNoMappings() {
            // Arrange
            List<String> headers = Arrays.asList("Unknown1", "Unknown2");
            Map<String, Attribute> attributeMap = createAttributesMap();

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act
            Map<Integer, List<ColumnDefinition>> result =
                    validator.columnDefinitionByColumnIndex(headers, attributeMap);

            // Assert
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Should handle null attribute in attributeMap")
        void nullAttributeInMap() {
            // Arrange
            List<String> headers = Arrays.asList("Name");
            Map<String, Attribute> attributeMap = new HashMap<>();
            attributeMap.put("name", null); // Null attribute

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act
            Map<Integer, List<ColumnDefinition>> result =
                    validator.columnDefinitionByColumnIndex(headers, attributeMap);

            // Assert
            assertNotNull(result.get(0));
            ColumnDefinition def = result.get(0).get(0);
            assertEquals("ATTRIBUTE", def.getFieldType());
            assertEquals("name", def.getFieldValue());
            assertNull(def.getTargetField());
            assertNull(def.getTargetValueType());
            assertNull(def.getMaxLength());
        }
    }

    @Nested
    @DisplayName("getRequiredAttributesNameExcludeSystemFields tests")
    class GetRequiredAttributesNameExcludeSystemFieldsTests {

        @Test
        @DisplayName("Should exclude system fields and nullable attributes")
        void excludeSystemAndNullableFields() {
            // Arrange
            Map<String, Attribute> attributes = createAttributesMap();
            when(entitySchema.getAttributes()).thenReturn(attributes);

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());
            // Act
            Set<String> result = validator.getRequiredAttributesNameExcludeSystemFields(
                    entitySchema, Set.of(DBConstants.TYPE_PROPERTY));

            // Assert
            assertEquals(1, result.size());
            assertTrue(result.contains("name"));
            assertFalse(result.contains("description"));
            assertFalse(result.contains("id"));
            assertFalse(result.contains("status"));
            assertFalse(result.contains(DBConstants.TYPE_PROPERTY));
        }

        @Test
        @DisplayName("Should handle empty attributes map")
        void emptyAttributesMap() {
            // Arrange
            when(entitySchema.getAttributes()).thenReturn(Collections.emptyMap());

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act
            Set<String> result = validator.getRequiredAttributesNameExcludeSystemFields(
                    entitySchema, Set.of(DBConstants.TYPE_PROPERTY));

            // Assert
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("Should handle empty excludes set")
        void emptyExcludesSet() {
            // Arrange
            Map<String, Attribute> attributes = createAttributesMap();
            when(entitySchema.getAttributes()).thenReturn(attributes);

            CommonHeaderValidator validator;
            validator = new CommonHeaderValidator(defaultMapping());

            // Act
            Set<String> result = validator.getRequiredAttributesNameExcludeSystemFields(
                    entitySchema, Collections.emptySet());

            // Assert
            assertEquals(2, result.size()); // name and type
            assertTrue(result.contains("name"));
            assertTrue(result.contains(DBConstants.TYPE_PROPERTY));
        }
    }

    @Test
    void testValidateLevelBasedBomHeaders_Success() {
        // Set up level-based BOM mappings
        List<DataMapping> bomMappings = new ArrayList<>();
        bomMappings.add(DataMapping.forLevelBasedBom("Level", "LEVEL"));
        bomMappings.add(DataMapping.forIdentifier("Part Number", "assembly"));
        bomMappings.add(DataMapping.forEntity("Description", "description", ObjectType.ATTRIBUTE));

        CommonHeaderValidator bomValidator = new CommonHeaderValidator(bomMappings);

        // Test with valid headers
        String[] headers = {"Level", "Part Number", "Description"};
        Map<String, Attribute> attributes = createAttributesMap();
        when(entitySchema.getAttributes()).thenReturn(attributes);

        assertDoesNotThrow(() -> bomValidator.validateRequiredHeaders(headers, entitySchema));
    }

    @Test
    void testValidateLevelBasedBomHeaders_MissingLevel() {
        // Set up level-based BOM mappings without level
        List<DataMapping> bomMappings = new ArrayList<>();
        bomMappings.add(DataMapping.forLevelBasedBom("Level", "LEVEL")); // Add the level mapping
        bomMappings.add(DataMapping.forIdentifier("Part Number", "assembly"));
        bomMappings.add(DataMapping.forEntity("Description", "description", ObjectType.ATTRIBUTE));

        CommonHeaderValidator bomValidator = new CommonHeaderValidator(bomMappings);

        // Test with missing level header
        String[] headers = {"Part Number", "Description"}; // Missing Level header
        Map<String, Attribute> attributes = createAttributesMap();
        when(entitySchema.getAttributes()).thenReturn(attributes);

        FileValidatorException exception = assertThrows(FileValidatorException.class,
                () -> bomValidator.validateRequiredHeaders(headers, entitySchema));
        assertTrue(exception.getMessage().contains("Level column is required"));
    }

    @Test
    void testValidateLevelBasedBomHeaders_MissingPartNumber() {
        // Set up level-based BOM mappings without part number
        List<DataMapping> bomMappings = new ArrayList<>();
        bomMappings.add(DataMapping.forLevelBasedBom("Level", "LEVEL"));
        bomMappings.add(DataMapping.forEntity("Description", "description", ObjectType.ATTRIBUTE));

        CommonHeaderValidator bomValidator = new CommonHeaderValidator(bomMappings);

        // Test with missing part number header
        String[] headers = {"Level", "Description"};
        Map<String, Attribute> attributes = createAttributesMap();
        when(entitySchema.getAttributes()).thenReturn(attributes);

        FileValidatorException exception = assertThrows(FileValidatorException.class,
                () -> bomValidator.validateRequiredHeaders(headers, entitySchema));
        assertTrue(exception.getMessage().contains("Part Number column is required"));
    }

    @Test
    void testValidateRegularBomHeaders() {
        // Set up regular BOM mappings
        List<DataMapping> bomMappings = new ArrayList<>();
        bomMappings.add(DataMapping.forRegularBom("Component", "componentId", ObjectType.COMPONENT));
        bomMappings.add(DataMapping.forRegularBom("Assembly", "assemblyId", ObjectType.ASSEMBLY));

        CommonHeaderValidator bomValidator = new CommonHeaderValidator(bomMappings);

        // Test with valid headers
        String[] headers = {"Component", "Assembly"};
        assertDoesNotThrow(() -> bomValidator.validateRequiredHeaders(headers, entitySchema));
    }

    @Test
    void testValidateEntityHeaders() {
        // Set up entity mappings
        List<DataMapping> entityMappings = new ArrayList<>();
        entityMappings.add(DataMapping.forEntity("Name", "name", ObjectType.ATTRIBUTE));
        entityMappings.add(DataMapping.forEntity("Description", "description", ObjectType.ATTRIBUTE));

        CommonHeaderValidator entityValidator = new CommonHeaderValidator(entityMappings);

        // Test with valid headers
        String[] headers = {"Name", "Description"};
        assertDoesNotThrow(() -> entityValidator.validateRequiredHeaders(headers, entitySchema));
    }

    @Test
    void testValidateRequiredHeaders_LevelBasedBom() {
        // Prepare test data
        List<DataMapping> mappings = new ArrayList<>();
        mappings.add(DataMapping.forLevelBasedBom("LEVEL", "level"));
        mappings.add(DataMapping.forIdentifier("Part Number", "assembly"));

        CommonHeaderValidator validator = new CommonHeaderValidator(mappings);

        // Test headers
        String[] headers = {"LEVEL", "Part Number", "Description"};
        Map<String, Attribute> attributes = createAttributesMap();
        when(entitySchema.getAttributes()).thenReturn(attributes);

        // Should not throw exception
        assertDoesNotThrow(() -> validator.validateRequiredHeaders(headers, entitySchema));
    }

    @Test
    void testValidateRequiredHeaders_MissingRequiredFields() {
        // Prepare test data
        List<DataMapping> mappings = new ArrayList<>();
        mappings.add(DataMapping.forEntity("Description", "description", ObjectType.ATTRIBUTE));

        CommonHeaderValidator validator = new CommonHeaderValidator(mappings);

        // Test headers with missing required field
        String[] headers = {"Description"}; // Missing "Name" which is required
        Map<String, Attribute> attributes = createAttributesMap();

        when(entitySchema.getAttributes()).thenReturn(attributes);

        // Should throw exception
        FileValidatorException exception = assertThrows(FileValidatorException.class,
                () -> validator.validateRequiredHeaders(headers, entitySchema));

        assertTrue(exception.getMessage().contains("Missing required fields"));
    }

    // Helper methods

    private Map<String, Attribute> createAttributesMap() {
        Map<String, Attribute> attributes = new HashMap<>();

        // Required non-system attribute
        Attribute nameAttr = new Attribute();
        nameAttr.setName("name");
        nameAttr.setIsNullable(false);
        nameAttr.setDefaultValue(null);
        nameAttr.setSystem(false);
        nameAttr.setType(Attribute.ValueType.STRING);

        attributes.put("name", nameAttr);

        // Nullable attribute (not required)
        Attribute descAttr = new Attribute();
        descAttr.setName("description");
        descAttr.setIsNullable(true);
        descAttr.setSystem(false);
        descAttr.setType(Attribute.ValueType.STRING);
        attributes.put("description", descAttr);

        // System attribute (should be excluded)
        Attribute idAttr = new Attribute();
        idAttr.setName("id");
        idAttr.setIsNullable(false);
        idAttr.setSystem(true);
        attributes.put("id", idAttr);

        // Attribute with default value (not required)
        Attribute statusAttr = new Attribute();
        statusAttr.setName("status");
        statusAttr.setIsNullable(false);
        statusAttr.setDefaultValue("ACTIVE");
        statusAttr.setSystem(false);
        attributes.put("status", statusAttr);

        // Type attribute (to be explicitly excluded in some tests)
        Attribute typeAttr = new Attribute();
        typeAttr.setName(DBConstants.TYPE_PROPERTY);
        typeAttr.setIsNullable(false);
        typeAttr.setDefaultValue(null);
        typeAttr.setSystem(false);
        attributes.put(DBConstants.TYPE_PROPERTY, typeAttr);

        return attributes;
    }

    private EntitySchema createTestEntitySchema() {
        EntitySchema schema = new EntitySchema();
        Map<String, Attribute> attributes = new HashMap<>();

        // Add required attribute
        Attribute nameAttr = new Attribute();
        nameAttr.setName("name");
        nameAttr.setIsNullable(false);
        nameAttr.setSystem(false);
        nameAttr.setType(Attribute.ValueType.STRING);
        attributes.put("name", nameAttr);

        schema.setAttributes(attributes);
        return schema;
    }

    private void assertColumnDefinition(
            List<ColumnDefinition> definitions,
            String expectedFieldType,
            String expectedFieldValue,
            String expectedTargetField,
            String expectedTargetValueType,
            Integer expectedMaxLength) {

        assertNotNull(definitions);
        assertEquals(1, definitions.size());
        ColumnDefinition def = definitions.get(0);
        assertEquals(expectedFieldType, def.getFieldType());
        assertEquals(expectedFieldValue, def.getFieldValue());
        assertEquals(expectedTargetField, def.getTargetField());
        assertEquals(expectedTargetValueType, def.getTargetValueType());
        assertEquals(expectedMaxLength, def.getMaxLength());
    }
}