/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.entity;

import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.client.dto.response.rule.RuleResult;
import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.migration.dto.response.FileImportSuccessResponse;
import com.tripudiotech.migration.entity.converter.ListRuleResultJsonConverter;
import com.tripudiotech.migration.entity.converter.MapJsonConverter;
import com.tripudiotech.base.event.embed.RowExtractedData;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @author: long.nguyen
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@FieldNameConstants
@Entity(name = "file_import_success")
/**
 * Entity class representing a successful file import record.
 * Contains information about the imported entity, its properties, and relation metadata.
 */
@Slf4j
public class FileImportSuccess {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "tenant", nullable = false)
    String tenantId;

    @Column(name = "file_import_id", nullable = false)
    Long fileImportId;

    @Column(name = "row_number", nullable = false)
    int rowNumber;

    @Column(name = "entity_id")
    String entityId;

    @Column(name = "entity_properties", nullable = false)
    @Convert(converter = MapJsonConverter.class)
    Map<String, Object> entityProperties;

    @Column(name = "relation_metadata")
    @Convert(converter = MapJsonConverter.class)
    Map<String, Object> relationMetadata;

    @Column(name = "business_rule_results")
    @Convert(converter = ListRuleResultJsonConverter.class)
    List<RuleResult> businessRuleResults;

    @Column(name = "raw_response")
    String rawResponse;

    @Column(name = "created_at")
    LocalDateTime createdAt;

    @Column(name = "updated_at")
    LocalDateTime updatedAt;

    @Column(name = "batch_number")
    private Integer batchNumber;

    public FileImportSuccessResponse toFileImportResponse() {
        return FileImportSuccessResponse.builder()
                .lineNumber(rowNumber)
                .createdAt(createdAt)
                .entityProperties(entityProperties)
                .entityId(entityId)
                .relationMetadata(relationMetadata)
                .businessRuleResults(businessRuleResults)
                .build();
    }

    /**
     * Creates a FileImportSuccess instance from a CreateEntityResponse
     *
     * @param tenantId The tenant ID
     * @param rowNumber The row number in the import file
     * @param fileImportId The file import ID
     * @param createEntityResponse The response from creating an entity
     * @return A FileImportSuccess instance
     */
    @SneakyThrows
    public static FileImportSuccess from(
            String tenantId,
            Integer rowNumber,
            Long fileImportId,
            @NonNull CreateEntityResponse createEntityResponse) {
        return from(tenantId, rowNumber, fileImportId, createEntityResponse, null);
    }

    /**
     * Creates a FileImportSuccess instance from a CreateEntityResponse and RowExtractedData
     *
     * @param tenantId The tenant ID
     * @param rowNumber The row number in the import file
     * @param fileImportId The file import ID
     * @param createEntityResponse The response from creating an entity
     * @param rowExtractedData The extracted data from the row, containing relation metadata
     * @return A FileImportSuccess instance
     */
    @SneakyThrows
    public static FileImportSuccess from(
            String tenantId,
            Integer rowNumber,
            Long fileImportId,
            @NonNull CreateEntityResponse createEntityResponse,
            RowExtractedData rowExtractedData
    ) {
        // Extract relation metadata from RowExtractedData if available
        Map<String, Object> relationMetadataMap = new HashMap<>();

        if (rowExtractedData != null && rowExtractedData.getRelationMetadataMap() != null) {
            rowExtractedData.getRelationMetadataMap().forEach((relationName, metadata) -> {
                Map<String, Object> relationData = new HashMap<>();

                // Create fromEntity data using information from rowExtractedData
                Map<String, Object> fromEntity = new HashMap<>();
                fromEntity.put("id", createEntityResponse.getId());

                // Use entity information from rowExtractedData which was processed by ColumnDefinitionHandler
                if (rowExtractedData.isUseExistingEntity()) {
                    fromEntity.put("name", rowExtractedData.getExistingEntityValue());
                    fromEntity.put("type", rowExtractedData.getExistingEntityType());
                } else {
                    fromEntity.put("name", Optional.ofNullable(createEntityResponse.getProperties())
                            .map(props -> props.get("name"))
                            .map(Object::toString)
                            .orElse(""));
                    fromEntity.put("type", metadata.getFromEntityType());
                }

                // Create toEntity data using metadata from ColumnDefinitionHandler
                Map<String, Object> toEntity = new HashMap<>();
                toEntity.put("id", metadata.getToEntityId());
                toEntity.put("name", Optional.ofNullable(metadata.getToEntityName()).orElse(""));
                toEntity.put("type", Optional.ofNullable(metadata.getToEntityType()).orElse(""));

                // Add to relation data
                relationData.put("fromEntity", fromEntity);
                relationData.put("toEntity", toEntity);
                relationData.put("relationName", relationName);

                // Add to metadata map using relation name as key
                relationMetadataMap.put(relationName, relationData);
                log.info("Added relation metadata for relation: {}", relationName);
            });
        }

        return FileImportSuccess.builder()
                .tenantId(tenantId)
                .rowNumber(rowNumber)
                .fileImportId(fileImportId)
                .entityId(createEntityResponse.getId())
                .rawResponse(JsonUtil.OBJECT_MAPPER.writeValueAsString(createEntityResponse))
                .entityProperties(
                        Map.of(
                                "properties", Optional.ofNullable(createEntityResponse.getProperties()).orElse(new HashMap<>()),
                                "id", createEntityResponse.getId(),
                                "lifecycle", Optional.ofNullable(createEntityResponse.getLifeCycle()).map(CreateEntityResponse.EntityLifeCycle::getLifeCycle)
                                        .map(val -> JsonUtil.OBJECT_MAPPER.convertValue(val, Map.class))
                                        .orElseGet(Collections::emptyMap),
                                "state", Optional.ofNullable(createEntityResponse.getState()).orElseGet(Collections::emptyMap),
                                "owner", Optional.ofNullable(createEntityResponse.getOwner()).orElseGet(Collections::emptyMap),
                                "relations", Optional.ofNullable(createEntityResponse.getRelations()).orElseGet(Collections::emptyList)
                        )
                )
                .relationMetadata(relationMetadataMap)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }
}
