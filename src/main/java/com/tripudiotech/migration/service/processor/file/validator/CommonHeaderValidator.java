package com.tripudiotech.migration.service.processor.file.validator;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.Attribute;
import com.tripudiotech.datalib.model.AttributeConstraint;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.validation.AttributeRegistry;
import com.tripudiotech.migration.validation.DataMappingProcessor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
public class CommonHeaderValidator {


    private final DataMappingProcessor processor;

    public CommonHeaderValidator(List<DataMapping> mappings) {
        processor = new DataMappingProcessor(mappings);
    }

    public void validateRequiredHeaders(
            @NonNull String[] headers,
            @NonNull EntitySchema entitySchema) {

        ObjectType importType = processor.determineImportType();

        // For level-based BOM imports, ensure required columns exist
        if (importType == ObjectType.LEVEL_BASED_BOM) {
            validateLevelBasedBomHeaders(headers);
            return; // Skip required fields validation for level-based BOM
        }

        if (headers.length == 0) {
            throw new FileValidatorException("No headers found in the file");
        }

        // Skip required fields validation for from existing entity import
        if (importType.equals(ObjectType.RELATION)) {
            log.info("Skipping required fields validation for from existing entity import");
            return;
        }

        Set<String> requiredAttributesNameExcludeSystemFields =
                getRequiredAttributesNameExcludeSystemFields(
                        entitySchema,
                        Set.of(DBConstants.TYPE_PROPERTY)
                );

        Set<String> importedAttributesName = new LinkedHashSet<>();

        AttributeRegistry attributeRegistry = new AttributeRegistry();

        Map<String, Attribute> attributeMapByName = entitySchema.getAttributes();

        attributeMapByName.forEach((k, v) -> attributeRegistry.registerAttribute(v));

        processor.getMappingsByImportType(ObjectType.ATTRIBUTE).forEach(dataMapping -> {
            importedAttributesName.add(dataMapping.getTarget());
        });

        validateRequiredHeader(
                importedAttributesName,
                requiredAttributesNameExcludeSystemFields,
                entitySchema.getEntityType().getName()
        );
    }

    private void validateLevelBasedBomHeaders(String[] headers) {
        boolean hasLevel = false;
        boolean hasPartNumber = false;

        for (String header : headers) {
            List<DataMapping> mappings = processor.getMappingsByColumn(header.toUpperCase());
            for (DataMapping mapping : mappings) {
                if (ObjectType.LEVEL_BASED_BOM.equals(mapping.getType())) {
                    hasLevel = true;
                }
                if (ObjectType.IDENTIFIER.equals(mapping.getType())) {
                    hasPartNumber = true;
                }
            }
        }

        if (!hasLevel) {
            throw new FileValidatorException("Level column is required for level-based BOM import");
        }
        if (!hasPartNumber) {
            throw new FileValidatorException("Part Number column is required for level-based BOM import");
        }
    }

    private void validateRequiredHeader(
            @NonNull Set<String> sourceHeaders,
            @NonNull Set<String> requiredAttributesNames,
            @NonNull String entityType
    ) {
        List<String> missingFields = new ArrayList<>();
        for (String requiredField : requiredAttributesNames) {
            boolean hasRequiredField = false;

            for (String attributeNameInLowercase : sourceHeaders) {
                if (requiredField.equalsIgnoreCase(attributeNameInLowercase.toLowerCase())) {
                    hasRequiredField = true;
                    break;
                }
            }

            if (!hasRequiredField) {
                missingFields.add(requiredField);
                log.error("Missing field: {}", requiredField);
            }
        }

        if (!missingFields.isEmpty()) {
            String errorMsg = String.format(
                    "Missing required fields. RequiredField: [%s] EntityType: %s",
                    String.join(",", missingFields),
                    entityType
            );
            throw new FileValidatorException(errorMsg);
        }
    }


    public Map<Integer, List<ColumnDefinition>> columnDefinitionByColumnIndex(
            @NonNull List<String> headers,
            Map<String, Attribute> attributeMap) {
        return columnDefinitionByColumnIndex(headers, attributeMap, null);
    }

    /**
     * Key = column index, Value = type of column: Attribute, Lifecycle, ....
     * This overloaded version allows specifying custom data mappings for a specific data type.
     *
     * @param headers
     * @param attributeMap
     * @param customMappings Optional custom mappings to use instead of the processor's mappings
     * @return
     */
    public Map<Integer, List<ColumnDefinition>> columnDefinitionByColumnIndex(
            @NonNull List<String> headers,
            Map<String, Attribute> attributeMap,
            List<DataMapping> customMappings) {

        Map<Integer, List<ColumnDefinition>> attributeMapByColumnIndex = new HashMap<>();

        for (int i = 0; i < headers.size(); i++) {
            String header = headers.get(i);
            List<DataMapping> eligibleTargets;

            if (customMappings != null) {
                // Use custom mappings if provided
                eligibleTargets = customMappings.stream()
                        .filter(mapping -> header.equalsIgnoreCase(mapping.getColumnName()))
                        .collect(Collectors.toList());
            } else {
                // Use processor's mappings
                eligibleTargets = processor.getMappingsByColumn(header.toUpperCase());
            }

            if (eligibleTargets.isEmpty()) {
                continue;
            }

            List<ColumnDefinition> columnDefinitions = new ArrayList<>();

            for (DataMapping mapping : eligibleTargets) {
                ColumnDefinition columnDefinition = new ColumnDefinition();
                columnDefinition.setFieldType(mapping.getType().name());
                columnDefinition.setFieldValue(mapping.getTarget());

                if (Objects.requireNonNull(mapping.getType()) == ObjectType.ATTRIBUTE) {

                    Optional.ofNullable(attributeMap.get(mapping.getTarget()))
                            .ifPresent(attribute -> {

                                columnDefinition.setTargetField(attribute.getName());
                                columnDefinition.setTargetValueType(attribute.getType().name());
                                columnDefinition.setTargetDataType(Optional.ofNullable(attribute.getConstraint()).map(
                                        AttributeConstraint::getDataType).orElse(null));
                                columnDefinition.setTargetPattern(attribute.getConstraint().getPattern());
                                columnDefinition.setTargetMinLength(Optional.
                                        ofNullable(attribute.getConstraint()).flatMap(
                                                attributeConstraint -> Optional.ofNullable(
                                                        attributeConstraint.getMinLength()).map(
                                                        Number::intValue)).orElse(null));
                                columnDefinition.setTargetMaxLength(Optional.
                                        ofNullable(attribute.getConstraint()).flatMap(
                                                attributeConstraint -> Optional.ofNullable(
                                                        attributeConstraint.getMaxLength()).map(
                                                        Number::intValue)).orElse(null));

                            });
                } else {
                    columnDefinition.setTargetField(mapping.getTarget());
                    columnDefinition.setTargetValueType(mapping.getType().name());
                }

                columnDefinitions.add(columnDefinition);
                attributeMapByColumnIndex.put(i, columnDefinitions);

                log.debug(
                        "Column order detected. ColumnIndex: {}, Definition: {} Value: {}",
                        i,
                        columnDefinition.getFieldType(),
                        columnDefinition.getFieldValue());
            }
        }
        return attributeMapByColumnIndex;
    }

    public Set<String> getRequiredAttributesNameExcludeSystemFields(
            EntitySchema entitySchema,
            Set<String> excludesFields
    ) {
        return entitySchema.getAttributes().entrySet().stream()
                .filter(e -> {
                    boolean isRequired = Boolean.FALSE.equals(e.getValue().getIsNullable()) &&
                                         Objects.isNull(e.getValue().getDefaultValue());
                    boolean isSystem = e.getValue().isSystem();
                    return !isSystem && isRequired;
                })
                .filter(e -> !excludesFields.contains(e.getValue().getName()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }
}
