package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.LengthValidation;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.EntityService;
import com.tripudiotech.migration.util.DelimiterUtils;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Handler for processing attribute column definitions.
 * Handles string arrays, length validation, pattern matching, and data type conversions.
 * 
 * Follows Single Responsibility Principle by focusing only on attribute processing.
 */
@Slf4j
@ApplicationScoped
public class AttributeColumnHandler implements ColumnHandler {

    private static final String STRING_ARRAY = "STRING_ARRAY";
    private static final char DEFAULT_DELIMITER = ',';

    @Inject
    EntityService entityService;

    @Override
    public Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport,
                           String columnValue, ColumnDefinition columnDefinition,
                           ParseSetting parseSetting, EntitySchema entitySchema,
                           RowExtractedData rowExtractedData) {
        
        if (StringUtils.isBlank(columnValue)) {
            return Uni.createFrom().voidItem();
        }

        if (STRING_ARRAY.equalsIgnoreCase(columnDefinition.getTargetValueType())) {
            return handleStringArray(columnValue, columnDefinition, parseSetting, rowExtractedData);
        } else {
            return handleSingleValue(tenantId, bearerToken, columnValue, columnDefinition, 
                                   parseSetting, rowExtractedData);
        }
    }

    private Uni<Void> handleStringArray(String columnValue, ColumnDefinition columnDefinition,
                                       ParseSetting parseSetting, RowExtractedData rowExtractedData) {
        char delimiter = parseSetting != null && parseSetting.getMultiListDelimiter() != null ?
                parseSetting.getMultiListDelimiter().getValue() : DEFAULT_DELIMITER;
        String[] values = DelimiterUtils.splitByDelimiter(columnValue, delimiter);
        rowExtractedData.getAttributeData().put(columnDefinition.getFieldValue(), values);
        return Uni.createFrom().voidItem();
    }

    private Uni<Void> handleSingleValue(String tenantId, String bearerToken, String columnValue,
                                       ColumnDefinition columnDefinition, ParseSetting parseSetting,
                                       RowExtractedData rowExtractedData) {
        
        // Apply length validations
        String processedValue = applyLengthValidations(columnValue, columnDefinition, parseSetting);
        
        // Apply pattern transformations
        processedValue = applyPatternTransformations(processedValue, columnDefinition);
        
        // Store the processed value
        rowExtractedData.getAttributeData().put(columnDefinition.getFieldValue(), processedValue);
        
        log.info("Extracted ATTRIBUTE. FileImport: {}, AttributeName: {}, Value: {}", 
                rowExtractedData.getRowNumber(), columnDefinition.getFieldValue(), processedValue);

        // Handle data type conversions if needed
        if (columnDefinition.getTargetDataType() != null) {
            return handleDataTypeConversion(tenantId, bearerToken, processedValue, 
                                           columnDefinition, rowExtractedData);
        }

        return Uni.createFrom().voidItem();
    }

    private String applyLengthValidations(String columnValue, ColumnDefinition columnDefinition,
                                        ParseSetting parseSetting) {
        String result = columnValue;
        
        // Max length validation
        if (columnDefinition.getTargetMaxLength() != null && 
            result.length() > columnDefinition.getTargetMaxLength()) {
            if (parseSetting != null && LengthValidation.TRUNCATE.equals(parseSetting.getLengthValidation())) {
                result = StringUtils.truncate(result, columnDefinition.getTargetMaxLength());
            }
        }

        // Min length validation
        if (columnDefinition.getTargetMinLength() != null && 
            result.length() < columnDefinition.getTargetMinLength()) {
            if (parseSetting != null && LengthValidation.TRUNCATE.equals(parseSetting.getLengthValidation())) {
                result = StringUtils.leftPad(result, columnDefinition.getTargetMinLength());
            }
        }

        return result;
    }

    private String applyPatternTransformations(String columnValue, ColumnDefinition columnDefinition) {
        if (columnDefinition.getTargetPattern() != null) {
            return columnValue.replaceAll(columnDefinition.getTargetPattern(), "");
        }
        return columnValue;
    }

    private Uni<Void> handleDataTypeConversion(String tenantId, String bearerToken, String columnValue,
                                              ColumnDefinition columnDefinition, RowExtractedData rowExtractedData) {
        String targetMapping = columnDefinition.getTarget();

        if (!targetMapping.contains(".")) {
            targetMapping = targetMapping + ".name";
        }

        if (targetMapping.contains(".")) {
            String entityType = targetMapping.split("\\.")[0];
            String fieldName = targetMapping.split("\\.")[1];

            if (fieldName.equalsIgnoreCase(SysRoot.Fields.id)) {
                return Uni.createFrom().voidItem();
            }

            return entityService.getUniqueEntityIdByAttributeWithCache(tenantId, bearerToken,
                            entityType, fieldName, columnValue.trim(), columnValue)
                    .flatMap(optionalCorrespondingId -> {
                        log.info("Found entity for {}.{}, ID: {}", entityType, fieldName, optionalCorrespondingId);

                        if (optionalCorrespondingId.isEmpty()) {
                            return Uni.createFrom().failure(new FileValidatorException(
                                    String.format("Entity not found for %s.%s = %s",
                                            entityType, fieldName, columnValue)));
                        }
                        rowExtractedData.getAttributeData().put(columnDefinition.getFieldValue(), 
                                                              optionalCorrespondingId.get());
                        return Uni.createFrom().voidItem();
                    });
        }

        return Uni.createFrom().voidItem();
    }
}
