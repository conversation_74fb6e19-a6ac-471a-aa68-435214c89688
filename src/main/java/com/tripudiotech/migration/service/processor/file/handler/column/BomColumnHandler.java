package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.dto.request.BomCreateRequest;
import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Handler for processing BOM (Bill of Materials) column definitions.
 * Handles both ASSEMBLY and COMPONENT types.
 * 
 * Follows Single Responsibility Principle by focusing only on BOM processing.
 */
@Slf4j
@ApplicationScoped
public class BomColumnHandler implements ColumnHandler {

    @Override
    public Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport,
                           String columnValue, ColumnDefinition columnDefinition,
                           ParseSetting parseSetting, EntitySchema entitySchema,
                           RowExtractedData rowExtractedData) {
        
        if (StringUtils.isBlank(columnValue)) {
            return Uni.createFrom().voidItem();
        }

        BomCreateRequest.Identifier identifier = createIdentifier(columnDefinition, columnValue);
        ObjectType objectType = ObjectType.valueOf(columnDefinition.getFieldType().toUpperCase());
        
        switch (objectType) {
            case ASSEMBLY -> {
                rowExtractedData.setAssembly(identifier);
                log.info("Extracted ASSEMBLY. FileImport: {}, Value: {}", 
                        rowExtractedData.getRowNumber(), columnValue);
            }
            case COMPONENT -> {
                rowExtractedData.setComponent(identifier);
                log.info("Extracted COMPONENT. FileImport: {}, Value: {}", 
                        rowExtractedData.getRowNumber(), columnValue);
            }
            default -> log.warn("Unsupported BOM object type: {}", objectType);
        }

        return Uni.createFrom().voidItem();
    }

    private BomCreateRequest.Identifier createIdentifier(ColumnDefinition columnDefinition, String columnValue) {
        BomCreateRequest.Identifier identifier = new BomCreateRequest.Identifier();
        identifier.setIdType(columnDefinition.getTargetField());
        identifier.setIdValue(columnValue);
        return identifier;
    }
}
