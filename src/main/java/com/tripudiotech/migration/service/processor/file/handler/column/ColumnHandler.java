package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;

/**
 * Base interface for column handlers following the Strategy pattern.
 * Each implementation handles a specific type of column definition.
 * 
 * This interface promotes the Single Responsibility Principle by ensuring
 * each handler is responsible for only one type of column processing.
 */
public interface ColumnHandler {
    
    /**
     * Process a column definition for a specific type
     * 
     * @param tenantId The tenant identifier
     * @param bearerToken Authentication token
     * @param fileImport The file import context
     * @param columnValue The value from the column
     * @param columnDefinition The column definition metadata
     * @param parseSetting Parse settings for the import
     * @param entitySchema The entity schema
     * @param rowExtractedData The row data being processed
     * @return A Uni representing the completion of processing
     */
    Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport, 
                     String columnValue, ColumnDefinition columnDefinition, 
                     ParseSetting parseSetting, EntitySchema entitySchema, 
                     RowExtractedData rowExtractedData);
}
