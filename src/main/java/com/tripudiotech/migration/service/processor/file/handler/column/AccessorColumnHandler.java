package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.RelationType;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;

/**
 * Handler for processing accessor/permission column definitions.
 * Handles role assignments and ownership relations.
 * 
 * Follows Single Responsibility Principle by focusing only on accessor processing.
 */
@Slf4j
@ApplicationScoped
public class AccessorColumnHandler implements ColumnHandler {

    private static final String OWNER = "OWNER";

    @Override
    public Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport,
                           String columnValue, ColumnDefinition columnDefinition,
                           ParseSetting parseSetting, EntitySchema entitySchema,
                           RowExtractedData rowExtractedData) {
        
        log.info("Extracted ACCESSOR. FileImport: {}, Role: {}, AgentId: {}", 
                fileImport.getId(), columnDefinition, columnValue);
        
        rowExtractedData.getPermission().setRole(columnDefinition.getFieldValue());
        rowExtractedData.getPermission().setAgentId(columnValue.trim());

        // Handle special case for OWNER role
        if (OWNER.equalsIgnoreCase(rowExtractedData.getPermission().getRole())) {
            handleOwnerRole(entitySchema, columnValue, rowExtractedData);
        }
        
        return Uni.createFrom().voidItem();
    }

    private void handleOwnerRole(EntitySchema entitySchema, String columnValue, RowExtractedData rowExtractedData) {
        String toEntityType = entitySchema.getRelationTypes().stream()
                .filter(rel -> rel.getName().equalsIgnoreCase(DBConstants.RELATION_OWNED_BY))
                .map(RelationType::getToEntityType)
                .findFirst()
                .orElse(null);

        // Add OWNER relation
        rowExtractedData.addRelation(DBConstants.RELATION_OWNED_BY, columnValue, null, toEntityType);
    }
}
