package com.tripudiotech.migration.service.helper;

import com.tripudiotech.base.client.AuthServiceClient;
import com.tripudiotech.base.client.dto.request.AccountRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.EntityService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import com.tripudiotech.datalib.db.DBConstants;

import java.util.Map;

@Slf4j
@ApplicationScoped
public class AuthServiceHelper {

    @Inject
    @RestClient
    AuthServiceClient authServiceClient;

    @Inject
    EntityService entityService;

    @Inject
    ConverterService converterService;

    @Inject
    ServiceExceptionHandler serviceExceptionHandler;

    public Uni<CreateEntityResponse> handleUserImport(String jwtToken, FileImport fileImport,
                                                      RowExtractedData rowExtractedData) {
        // Create user under company
        String firstName = String.valueOf(rowExtractedData.getAttributes().get("firstName"));
        String lastName = String.valueOf(rowExtractedData.getAttributes().get("lastName"));

        log.info("Importing user. FileId: {}, RowNum: {}, Data: {} ", fileImport.getId(),
                rowExtractedData.getRowNumber(), rowExtractedData.getAttributeData());

        // Verify company exists
        if (rowExtractedData.getExistingEntityValue() == null) {
            log.error("Company not found. FileId: {}, RowNum: {}", fileImport.getId(), rowExtractedData.getRowNumber());
            return serviceExceptionHandler.handleServiceException(
                    fileImport,
                    rowExtractedData.getRowNumber(),
                    "FIND_ENTITY",
                    null,
                    new ServiceException(fileImport.getTenantId(), BusinessErrorCode.RECORD_NOT_FOUND, "Company not found")
            );
        }

        log.info("Creating user under company. FileId: {}, RowNum: {}, Company: {}, " +
                 "Username: {}, FirstName: {}, LastName: {}",
                fileImport.getId(), rowExtractedData.getRowNumber(),
                rowExtractedData.getExistingEntityValue(),
                rowExtractedData.getAgentId(), firstName, lastName);

        return entityService.findEntityByField(
                        jwtToken,
                        fileImport.getTenantId(),
                        "InternalCompany",
                        rowExtractedData.getExistingEntityField(),
                        rowExtractedData.getExistingEntityValue())
                .flatMap(response -> {

                    response.bufferEntity();

                    // Parse the response to get the entity ID
                    PageResponse pageResponse =
                            response.readEntity(PageResponse.class);

                    // Check if we found the entity
                    if (pageResponse.getData() == null || pageResponse.getData().isEmpty()) {
                        String errorMsg = String.format("Existing entity not found: %s.%s = %s",
                                rowExtractedData.getExistingEntityType(),
                                rowExtractedData.getExistingEntityField(),
                                rowExtractedData.getExistingEntityValue());
                        log.error(errorMsg);
                        return Uni.createFrom().failure(new FileDataPopulateException(
                                errorMsg,
                                rowExtractedData.getRowNumber(),
                                "FIND_ENTITY",
                                null,
                                null
                        ));
                    }

                    EntityWithPermission entity = converterService.convertMapToValue(
                            (Map<String, Object>) pageResponse.getData().get(0),
                            EntityWithPermission.class
                    );

                    String companyId = entity.getId();
                    log.info("FileId: {}, RowNum: {}, Found company. CompanyId: {}", fileImport.getId(), rowExtractedData.getRowNumber(), companyId);

                    if (companyId == null || companyId.isEmpty()) {
                        String errorMsg = "Company ID is null or empty";
                        log.error("FileId: {}, RowNum: {}, Error: {}", fileImport.getId(), rowExtractedData.getRowNumber(), errorMsg);

                        serviceExceptionHandler.handleServiceException(
                                fileImport,
                                rowExtractedData.getRowNumber(),
                                "CREATE_USER",
                                null,
                                new ServiceException(fileImport.getTenantId(), BusinessErrorCode.RECORD_NOT_FOUND, "Company not found")
                        );
                    }

                    log.info("FileId: {}, RowNum: {}, Creating user under company with ID: {}",
                            fileImport.getId(), rowExtractedData.getRowNumber(), companyId);

                    AccountRequest accountRequest = AccountRequest.builder()
                            .firstName(firstName)
                            .lastName(lastName)
                            .username(rowExtractedData.getAgentId())
                            .properties(rowExtractedData.getAttributes())
                            .build();

                    return authServiceClient.createUserUnderCompany(fileImport.getTenantId(),
                                    jwtToken,
                                    companyId, accountRequest)
                            .map(createResponse -> {
                                createResponse.bufferEntity();
                                CreateEntityResponse createEntityResponse = createResponse.readEntity(CreateEntityResponse.class);
                                log.info("CREATE_USER succeeded with EntityID {}. FileImportId: {}, RowNumber: {}",
                                        createEntityResponse.getId(), fileImport.getId(), rowExtractedData.getRowNumber());
                                return createEntityResponse;

                            }).onFailure().recoverWithUni(throwable -> {

                                if (throwable instanceof ServiceException serviceException) {

                                    serviceException.setParameters(
                                            Map
                                                    .of("companyId", companyId,
                                                            "firstName", firstName,
                                                            "lastName", lastName,
                                                            "username", rowExtractedData.getAgentId()
                                                    ));

                                    return serviceExceptionHandler.handleServiceException(
                                            fileImport,
                                            rowExtractedData.getRowNumber(),
                                            "CREATE_USER",
                                            accountRequest,
                                            serviceException
                                    );
                                }

                                FileDataPopulateException fileDataPopulateException = serviceExceptionHandler.createFileDataPopulateException(
                                        "CREATE_USER",
                                        rowExtractedData.getRowNumber(),
                                        throwable,
                                        accountRequest
                                );

                                log.error("Unexpected error creating user. FileId: {}, RowNum: {}",
                                        fileImport.getId(), rowExtractedData.getRowNumber(), throwable);

                                return serviceExceptionHandler.fileDataPopulateExceptionHandler.handle(
                                                fileImport,
                                                fileDataPopulateException
                                        )
                                        .flatMap(vr -> Uni.createFrom().failure(fileDataPopulateException));
                            });
                });
    }
}
