/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service;

import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.helper.AuthServiceHelper;
import com.tripudiotech.migration.service.helper.BomImportHelper;
import com.tripudiotech.migration.service.helper.EntityImportHelper;
import com.tripudiotech.migration.service.helper.MultipleEntityTypeHelper;
import com.tripudiotech.migration.service.helper.PermissionHelper;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

/**
 * Service for populating data from imported files into the internal database.
 * Delegates to specialized helper classes for different types of imports.
 *
 * @author: long.nguyen
 **/
@ApplicationScoped
@Slf4j
public class DataPopulateService {

    @Inject
    BomImportHelper bomImportHelper;

    @Inject
    EntityImportHelper entityImportHelper;

    @Inject
    PermissionHelper permissionHelper;

    @Inject
    MultipleEntityTypeHelper multipleEntityTypeHelper;

    @Inject
    AuthServiceHelper authServiceHelper;

    /**
     * Main entry point for populating data from an imported file row.
     * Delegates to specialized handlers based on the type of data.
     *
     * @param jwtToken         Authentication token
     * @param fileImport       The file import being processed
     * @param rowExtractedData The data extracted from a row in the import file
     * @return A Uni containing the created entity response
     */
    public Uni<CreateEntityResponse> populateDataToInternalDatabase(
            @NonNull String jwtToken,
            @NonNull FileImport fileImport,
            @NonNull RowExtractedData rowExtractedData
    ) {
        try {
            // For multiple data types import, we may have a different entity type for this row
            String entityType = fileImport.getEntityType();
            if (rowExtractedData.getDataType() != null) {
                entityType = rowExtractedData.getDataType();
                log.info(
                        "Multiple data types import detected. Using entity type from row data. FileImportId: {}, EntityType: {}, RowNumber: {}",
                        fileImport.getId(),
                        entityType,
                        rowExtractedData.getRowNumber()
                );
            }

            // Get the schema for this entity type
            EntitySchema entitySchema = multipleEntityTypeHelper.getSchemaForEntityType(entityType, fileImport);
            if (entitySchema == null) {
                return Uni.createFrom().failure(
                        new FileValidatorException("Schema not found for entity type: " + entityType));
            }

            log.info(
                    "Ready to populate data to entity service. FileImportId: {}, EntityType: {}, RowNumber: {}",
                    fileImport.getId(),
                    entityType,
                    rowExtractedData.getRowNumber()
            );

            if(rowExtractedData.importUser()){
                log.info("Importing user. FileImportId: {}, RowNumber: {}",
                        fileImport.getId(),
                        rowExtractedData.getRowNumber());
                return authServiceHelper.handleUserImport(jwtToken, fileImport, rowExtractedData);
            }

            // If we're using an existing entity, handle it differently
            if (rowExtractedData.isUseExistingEntity()) {
                return entityImportHelper.handleExistingEntityImport(jwtToken, fileImport, rowExtractedData);
            }

            // Handle BOM-specific logic
            if (rowExtractedData.isBom()) {
                return bomImportHelper.handleBomImport(jwtToken, fileImport, rowExtractedData);
            }

            // Handle regular entity import
            return entityImportHelper.handleEntityImport(jwtToken, fileImport, rowExtractedData);
        } catch (Exception e) {
            log.error("Unexpected error during data population. FileId: {}, RowNum: {}",
                    fileImport.getId(), rowExtractedData.getRowNumber(), e);
            return Uni.createFrom().failure(e);
        }
    }

    /**
     * Grants permissions for an entity.
     * Delegates to the PermissionHelper.
     *
     * @param token                Authentication token
     * @param fileImport           The file import being processed
     * @param createEntityResponse The response from entity creation
     * @param rowExtractedData     The data extracted from a row in the import file
     * @return A Uni that completes when the permission has been granted
     */
    public Uni<Void> grantPermission(
            @NonNull String token,
            @NonNull FileImport fileImport,
            @NonNull CreateEntityResponse createEntityResponse,
            @NonNull RowExtractedData rowExtractedData
    ) {
        return permissionHelper.grantPermission(token, fileImport, createEntityResponse, rowExtractedData);
    }
}
